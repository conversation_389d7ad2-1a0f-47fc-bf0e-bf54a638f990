const express = require('express');
const router = express.Router();
const {
  updateBusinessLocation,
  getNearbyBusinesses,
  getBusinessLocation,
  searchBusinessesByLocation
} = require('../controllers/locationController');
const auth = require('../middlewares/auth');

// All location routes require authentication
router.use(auth);

// Business location management routes
router.put('/update-location', updateBusinessLocation);
router.get('/business-location', getBusinessLocation);

// Public location-based search routes
router.post('/nearby-businesses', getNearbyBusinesses);
router.get('/search-businesses', searchBusinessesByLocation);

module.exports = router;
