const User = require('../models/User');

// Update business location
exports.updateBusinessLocation = async (req, res) => {
  try {
    const { latitude, longitude, formattedAddress } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ 
        message: 'Latitude and longitude are required' 
      });
    }

    // Validate latitude and longitude ranges
    if (latitude < -90 || latitude > 90) {
      return res.status(400).json({ 
        message: 'Latitude must be between -90 and 90' 
      });
    }

    if (longitude < -180 || longitude > 180) {
      return res.status(400).json({ 
        message: 'Longitude must be between -180 and 180' 
      });
    }

    // Find user and check if they are a business
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.userType !== 'Business') {
      return res.status(403).json({ 
        message: 'Only business users can set location' 
      });
    }

    // Update user location
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        formattedAddress: formattedAddress || null
      },
      { new: true, runValidators: true }
    ).select('-password -resetPasswordToken -resetPasswordExpires');

    res.status(200).json({
      message: 'Location updated successfully',
      user: updatedUser
    });

  } catch (error) {
    console.error('Error updating business location:', error);
    res.status(500).json({ 
      message: 'Error updating location', 
      error: error.message 
    });
  }
};

// Get nearby businesses
exports.getNearbyBusinesses = async (req, res) => {
  try {
    const { latitude, longitude, radius = 10, page = 1, limit = 20 } = req.body;

    // Validate required fields
    if (!latitude || !longitude) {
      return res.status(400).json({ 
        message: 'Latitude and longitude are required' 
      });
    }

    // Validate latitude and longitude ranges
    if (latitude < -90 || latitude > 90) {
      return res.status(400).json({ 
        message: 'Latitude must be between -90 and 90' 
      });
    }

    if (longitude < -180 || longitude > 180) {
      return res.status(400).json({ 
        message: 'Longitude must be between -180 and 180' 
      });
    }

    // Convert radius from kilometers to degrees (approximate)
    // 1 degree ≈ 111 km
    const radiusInDegrees = radius / 111;

    // Find businesses within the specified radius
    const businesses = await User.find({
      userType: 'Business',
      latitude: { $exists: true, $ne: null },
      longitude: { $exists: true, $ne: null },
      latitude: {
        $gte: latitude - radiusInDegrees,
        $lte: latitude + radiusInDegrees
      },
      longitude: {
        $gte: longitude - radiusInDegrees,
        $lte: longitude + radiusInDegrees
      }
    })
    .select('-password -resetPasswordToken -resetPasswordExpires')
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .sort({ name: 1 });

    // Calculate actual distance for each business
    const businessesWithDistance = businesses.map(business => {
      const distance = calculateDistance(
        latitude, 
        longitude, 
        business.latitude, 
        business.longitude
      );
      
      return {
        ...business.toObject(),
        distance: Math.round(distance * 100) / 100 // Round to 2 decimal places
      };
    }).filter(business => business.distance <= radius);

    // Sort by distance
    businessesWithDistance.sort((a, b) => a.distance - b.distance);

    const total = businessesWithDistance.length;
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      message: 'Nearby businesses retrieved successfully',
      businesses: businessesWithDistance,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalBusinesses: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error getting nearby businesses:', error);
    res.status(500).json({ 
      message: 'Error getting nearby businesses', 
      error: error.message 
    });
  }
};

// Get business location
exports.getBusinessLocation = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findById(userId)
      .select('latitude longitude formattedAddress userType name');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.userType !== 'Business') {
      return res.status(403).json({ 
        message: 'Only business users can access location data' 
      });
    }

    res.status(200).json({
      message: 'Business location retrieved successfully',
      location: {
        latitude: user.latitude,
        longitude: user.longitude,
        formattedAddress: user.formattedAddress,
        hasLocation: !!(user.latitude && user.longitude)
      }
    });

  } catch (error) {
    console.error('Error getting business location:', error);
    res.status(500).json({ 
      message: 'Error getting business location', 
      error: error.message 
    });
  }
};

// Helper function to calculate distance between two points using Haversine formula
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in kilometers
  return distance;
}

// Search businesses by location and other criteria
exports.searchBusinessesByLocation = async (req, res) => {
  try {
    const { 
      latitude, 
      longitude, 
      radius = 10, 
      query, 
      page = 1, 
      limit = 20 
    } = req.query;

    // Build search filter
    let filter = { 
      userType: 'Business',
      latitude: { $exists: true, $ne: null },
      longitude: { $exists: true, $ne: null }
    };

    // Add text search if query provided
    if (query) {
      filter.$or = [
        { name: new RegExp(query, 'i') },
        { formattedAddress: new RegExp(query, 'i') }
      ];
    }

    // If location provided, filter by radius
    if (latitude && longitude) {
      const radiusInDegrees = radius / 111;
      filter.latitude = {
        $gte: parseFloat(latitude) - radiusInDegrees,
        $lte: parseFloat(latitude) + radiusInDegrees
      };
      filter.longitude = {
        $gte: parseFloat(longitude) - radiusInDegrees,
        $lte: parseFloat(longitude) + radiusInDegrees
      };
    }

    const businesses = await User.find(filter)
      .select('-password -resetPasswordToken -resetPasswordExpires')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ name: 1 });

    // Calculate distance if location provided
    let businessesWithDistance = businesses.map(business => {
      const businessObj = business.toObject();
      if (latitude && longitude) {
        businessObj.distance = Math.round(
          calculateDistance(
            parseFloat(latitude), 
            parseFloat(longitude), 
            business.latitude, 
            business.longitude
          ) * 100
        ) / 100;
      }
      return businessObj;
    });

    // Filter by actual radius and sort by distance if location provided
    if (latitude && longitude) {
      businessesWithDistance = businessesWithDistance
        .filter(business => business.distance <= radius)
        .sort((a, b) => a.distance - b.distance);
    }

    const total = businessesWithDistance.length;
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      message: 'Businesses retrieved successfully',
      businesses: businessesWithDistance,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalBusinesses: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error searching businesses by location:', error);
    res.status(500).json({ 
      message: 'Error searching businesses', 
      error: error.message 
    });
  }
};
