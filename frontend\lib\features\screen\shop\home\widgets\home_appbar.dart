import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';
import '../../../../../utlis/constants/colors.dart';
import '../../../../../utlis/constants/size.dart';
import '../../../../../provider/auth_provider/loginprovider.dart';
import '../../../../../provider/location_provider/location_provider.dart';
import '../../../location/location_selection_screen.dart';

class HomeAppBar extends StatelessWidget {
  const HomeAppBar({super.key});

  void _handleLocationTap(BuildContext context, LoginProvider loginProvider) {
    // Check if user is logged in
    if (loginProvider.user == null) {
      _showLoginRequiredDialog(context);
      return;
    }

    // Check if user is a business user
    if (loginProvider.user!.userType == 'Business') {
      // Navigate to location selection screen for business users
      Get.to(() => const LocationSelectionScreen());
    } else {
      // For pet owners, show nearby businesses (to be implemented)
      _showComingSoonDialog(context);
    }
  }

  void _showLoginRequiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Login Required'),
        content: const Text('Please login to access location features.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showComingSoonDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Coming Soon'),
        content:
            const Text('Nearby businesses feature will be available soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: AppSizes.xl,
        left: AppSizes.md,
        right: AppSizes.md,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Consumer<LoginProvider>(
            builder: (context, loginProvider, child) {
              return GestureDetector(
                onTap: () => _handleLocationTap(context, loginProvider),
                child: Row(
                  children: [
                    Icon(Icons.location_pin,
                        size: AppSizes.iconMd, color: Colors.black),
                    SizedBox(width: AppSizes.sm),
                    const Text(
                      "My Location",
                      style: TextStyle(color: AppColors.primary),
                    ),
                  ],
                ),
              );
            },
          ),
          Stack(
            children: [
              Icon(Icons.notifications_none, size: AppSizes.iconMd),
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
